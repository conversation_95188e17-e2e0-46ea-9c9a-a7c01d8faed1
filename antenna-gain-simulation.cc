#include "ns3/core-module.h"
#include "ns3/satellite-module.h"
#include <iostream>
#include <fstream>

using namespace ns3;

NS_LOG_COMPONENT_DEFINE ("AntennaGainSimulation");

int main (int argc, char *argv[])
{
  // 启用日志
  LogComponentEnable ("AntennaGainSimulation", LOG_LEVEL_INFO);
  
  // 创建卫星配置
  Ptr<SatConf> satConf = CreateObject<SatConf> ();
  
  // 获取天线增益模式
  Ptr<SatAntennaGainPatternContainer> antennaGainPatterns = satConf->GetAntennaGainPatterns ();
  
  // 选择要分析的波束ID（例如波束1）
  uint32_t beamId = 1;
  Ptr<SatAntennaGainPattern> agp = antennaGainPatterns->GetAntennaGainPattern (beamId);
  
  if (agp == nullptr)
    {
      NS_LOG_ERROR ("无法获取波束 " << beamId << " 的天线增益模式");
      return -1;
    }
  
  // 创建输出文件
  std::ofstream outFile ("antenna-gain-map.csv");
  outFile << "Latitude,Longitude,AntennaGain_dB,AntennaGain_Linear" << std::endl;
  
  // 定义仿真区域范围（根据天线模式文件调整）
  double latMin = 17.0;   // 最小纬度
  double latMax = 51.0;   // 最大纬度
  double lonMin = -46.0;  // 最小经度
  double lonMax = 96.0;   // 最大经度
  
  // 设置采样步长（度）
  double latStep = 0.5;   // 纬度步长
  double lonStep = 0.5;   // 经度步长
  
  NS_LOG_INFO ("开始仿真天线增益分布...");
  NS_LOG_INFO ("波束ID: " << beamId);
  NS_LOG_INFO ("纬度范围: " << latMin << " 到 " << latMax);
  NS_LOG_INFO ("经度范围: " << lonMin << " 到 " << lonMax);
  NS_LOG_INFO ("采样步长: " << latStep << "° x " << lonStep << "°");
  
  int totalPoints = 0;
  int validPoints = 0;
  
  // 遍历地理区域
  for (double lat = latMin; lat <= latMax; lat += latStep)
    {
      for (double lon = lonMin; lon <= lonMax; lon += lonStep)
        {
          totalPoints++;
          
          // 创建地理坐标
          GeoCoordinate geoCoord (lat, lon, 0.0);
          
          // 获取该位置的天线增益（线性值）
          double antennaGain_linear = agp->GetAntennaGain_lin (geoCoord);
          
          // 检查是否为有效值（非NaN且非零）
          if (!std::isnan (antennaGain_linear) && antennaGain_linear > 0.0)
            {
              validPoints++;
              
              // 转换为dB值
              double antennaGain_dB = 10.0 * std::log10 (antennaGain_linear);
              
              // 写入文件
              outFile << lat << "," << lon << "," << antennaGain_dB << "," << antennaGain_linear << std::endl;
              
              // 每1000个有效点输出一次进度
              if (validPoints % 1000 == 0)
                {
                  NS_LOG_INFO ("已处理 " << validPoints << " 个有效点");
                }
            }
        }
    }
  
  outFile.close ();
  
  NS_LOG_INFO ("仿真完成！");
  NS_LOG_INFO ("总采样点数: " << totalPoints);
  NS_LOG_INFO ("有效点数: " << validPoints);
  NS_LOG_INFO ("覆盖率: " << (double)validPoints / totalPoints * 100.0 << "%");
  NS_LOG_INFO ("结果已保存到: antenna-gain-map.csv");
  
  // 计算统计信息
  std::ifstream inFile ("antenna-gain-map.csv");
  std::string line;
  std::getline (inFile, line); // 跳过标题行
  
  double maxGain_dB = -1000.0;
  double minGain_dB = 1000.0;
  double sumGain_dB = 0.0;
  int count = 0;
  
  while (std::getline (inFile, line))
    {
      std::istringstream iss (line);
      std::string token;
      
      // 跳过纬度和经度
      std::getline (iss, token, ',');
      std::getline (iss, token, ',');
      
      // 读取增益值
      std::getline (iss, token, ',');
      double gain_dB = std::stod (token);
      
      maxGain_dB = std::max (maxGain_dB, gain_dB);
      minGain_dB = std::min (minGain_dB, gain_dB);
      sumGain_dB += gain_dB;
      count++;
    }
  
  inFile.close ();
  
  if (count > 0)
    {
      double avgGain_dB = sumGain_dB / count;
      NS_LOG_INFO ("天线增益统计:");
      NS_LOG_INFO ("  最大增益: " << maxGain_dB << " dB");
      NS_LOG_INFO ("  最小增益: " << minGain_dB << " dB");
      NS_LOG_INFO ("  平均增益: " << avgGain_dB << " dB");
      NS_LOG_INFO ("  增益范围: " << (maxGain_dB - minGain_dB) << " dB");
    }
  
  return 0;
}
