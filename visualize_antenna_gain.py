#!/usr/bin/env python3
"""
天线增益分布可视化脚本
用于显示卫星波束的天线增益模式
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from mpl_toolkits.basemap import Basemap
import seaborn as sns

def load_antenna_gain_data(filename):
    """加载天线增益数据"""
    try:
        df = pd.read_csv(filename)
        print(f"成功加载数据: {len(df)} 个数据点")
        return df
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
        print("请先运行 antenna-gain-simulation 程序生成数据")
        return None

def plot_antenna_gain_heatmap(df, beam_id=1):
    """绘制天线增益热力图"""
    plt.figure(figsize=(15, 10))
    
    # 创建网格数据
    lat_unique = sorted(df['Latitude'].unique())
    lon_unique = sorted(df['Longitude'].unique())
    
    # 创建网格
    lat_grid, lon_grid = np.meshgrid(lat_unique, lon_unique, indexing='ij')
    gain_grid = np.full(lat_grid.shape, np.nan)
    
    # 填充增益数据
    for _, row in df.iterrows():
        lat_idx = lat_unique.index(row['Latitude'])
        lon_idx = lon_unique.index(row['Longitude'])
        gain_grid[lat_idx, lon_idx] = row['AntennaGain_dB']
    
    # 绘制热力图
    plt.subplot(2, 2, 1)
    im1 = plt.imshow(gain_grid, extent=[min(lon_unique), max(lon_unique), 
                                       min(lat_unique), max(lat_unique)],
                     aspect='auto', origin='lower', cmap='jet')
    plt.colorbar(im1, label='天线增益 (dB)')
    plt.xlabel('经度 (°)')
    plt.ylabel('纬度 (°)')
    plt.title(f'波束 {beam_id} 天线增益分布 (热力图)')
    plt.grid(True, alpha=0.3)
    
    # 绘制等高线图
    plt.subplot(2, 2, 2)
    contour = plt.contour(lon_grid, lat_grid, gain_grid, levels=20, colors='black', alpha=0.6)
    contourf = plt.contourf(lon_grid, lat_grid, gain_grid, levels=20, cmap='jet')
    plt.colorbar(contourf, label='天线增益 (dB)')
    plt.clabel(contour, inline=True, fontsize=8)
    plt.xlabel('经度 (°)')
    plt.ylabel('纬度 (°)')
    plt.title(f'波束 {beam_id} 天线增益等高线图')
    plt.grid(True, alpha=0.3)
    
    # 绘制3D表面图
    ax3d = plt.subplot(2, 2, 3, projection='3d')
    surf = ax3d.plot_surface(lon_grid, lat_grid, gain_grid, cmap='jet', alpha=0.8)
    ax3d.set_xlabel('经度 (°)')
    ax3d.set_ylabel('纬度 (°)')
    ax3d.set_zlabel('天线增益 (dB)')
    ax3d.set_title(f'波束 {beam_id} 天线增益 3D 视图')
    plt.colorbar(surf, ax=ax3d, shrink=0.5, label='天线增益 (dB)')
    
    # 绘制增益分布直方图
    plt.subplot(2, 2, 4)
    plt.hist(df['AntennaGain_dB'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.xlabel('天线增益 (dB)')
    plt.ylabel('频次')
    plt.title(f'波束 {beam_id} 天线增益分布直方图')
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    stats_text = f"""统计信息:
最大增益: {df['AntennaGain_dB'].max():.2f} dB
最小增益: {df['AntennaGain_dB'].min():.2f} dB
平均增益: {df['AntennaGain_dB'].mean():.2f} dB
标准差: {df['AntennaGain_dB'].std():.2f} dB
数据点数: {len(df)}"""
    
    plt.figtext(0.02, 0.02, stats_text, fontsize=10, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    
    plt.tight_layout()
    plt.savefig(f'antenna_gain_beam_{beam_id}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_gain_cross_sections(df, beam_id=1):
    """绘制天线增益截面图"""
    plt.figure(figsize=(15, 5))
    
    # 找到增益最大值的位置
    max_gain_idx = df['AntennaGain_dB'].idxmax()
    center_lat = df.loc[max_gain_idx, 'Latitude']
    center_lon = df.loc[max_gain_idx, 'Longitude']
    
    print(f"波束中心位置: 纬度 {center_lat}°, 经度 {center_lon}°")
    print(f"最大增益: {df.loc[max_gain_idx, 'AntennaGain_dB']:.2f} dB")
    
    # 纬度截面（固定经度）
    plt.subplot(1, 3, 1)
    lat_section = df[abs(df['Longitude'] - center_lon) < 0.1].copy()
    lat_section = lat_section.sort_values('Latitude')
    plt.plot(lat_section['Latitude'], lat_section['AntennaGain_dB'], 'b-o', markersize=3)
    plt.xlabel('纬度 (°)')
    plt.ylabel('天线增益 (dB)')
    plt.title(f'纬度截面 (经度≈{center_lon:.1f}°)')
    plt.grid(True, alpha=0.3)
    
    # 经度截面（固定纬度）
    plt.subplot(1, 3, 2)
    lon_section = df[abs(df['Latitude'] - center_lat) < 0.1].copy()
    lon_section = lon_section.sort_values('Longitude')
    plt.plot(lon_section['Longitude'], lon_section['AntennaGain_dB'], 'r-o', markersize=3)
    plt.xlabel('经度 (°)')
    plt.ylabel('天线增益 (dB)')
    plt.title(f'经度截面 (纬度≈{center_lat:.1f}°)')
    plt.grid(True, alpha=0.3)
    
    # 径向截面（从中心向外）
    plt.subplot(1, 3, 3)
    df['distance'] = np.sqrt((df['Latitude'] - center_lat)**2 + 
                            (df['Longitude'] - center_lon)**2)
    
    # 按距离分组并计算平均增益
    distance_bins = np.linspace(0, df['distance'].max(), 50)
    binned_data = []
    
    for i in range(len(distance_bins)-1):
        mask = (df['distance'] >= distance_bins[i]) & (df['distance'] < distance_bins[i+1])
        if mask.sum() > 0:
            avg_distance = distance_bins[i:i+2].mean()
            avg_gain = df[mask]['AntennaGain_dB'].mean()
            binned_data.append([avg_distance, avg_gain])
    
    if binned_data:
        binned_df = pd.DataFrame(binned_data, columns=['distance', 'gain'])
        plt.plot(binned_df['distance'], binned_df['gain'], 'g-o', markersize=3)
        plt.xlabel('距离波束中心 (°)')
        plt.ylabel('平均天线增益 (dB)')
        plt.title('径向增益分布')
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'antenna_gain_cross_sections_beam_{beam_id}.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_beam_coverage(df, gain_threshold_dB=-3):
    """分析波束覆盖特性"""
    print(f"\n=== 波束覆盖分析 ===")
    print(f"增益阈值: {gain_threshold_dB} dB")
    
    # 计算覆盖区域
    coverage_area = df[df['AntennaGain_dB'] >= gain_threshold_dB]
    total_points = len(df)
    coverage_points = len(coverage_area)
    
    print(f"总采样点数: {total_points}")
    print(f"覆盖点数: {coverage_points}")
    print(f"覆盖率: {coverage_points/total_points*100:.2f}%")
    
    if len(coverage_area) > 0:
        # 计算覆盖区域的地理范围
        lat_range = coverage_area['Latitude'].max() - coverage_area['Latitude'].min()
        lon_range = coverage_area['Longitude'].max() - coverage_area['Longitude'].min()
        
        print(f"覆盖区域纬度范围: {lat_range:.2f}°")
        print(f"覆盖区域经度范围: {lon_range:.2f}°")
        
        # 估算覆盖面积（粗略计算）
        # 1度纬度 ≈ 111 km, 1度经度 ≈ 111 * cos(纬度) km
        avg_lat = coverage_area['Latitude'].mean()
        lat_km = lat_range * 111
        lon_km = lon_range * 111 * np.cos(np.radians(avg_lat))
        area_km2 = lat_km * lon_km
        
        print(f"估算覆盖面积: {area_km2:.0f} km²")

def main():
    """主函数"""
    print("=== 卫星天线增益分布分析工具 ===")
    
    # 加载数据
    df = load_antenna_gain_data('antenna-gain-map.csv')
    if df is None:
        return
    
    beam_id = 1  # 可以修改为其他波束ID
    
    print(f"\n分析波束 {beam_id} 的天线增益分布...")
    
    # 绘制主要图表
    plot_antenna_gain_heatmap(df, beam_id)
    
    # 绘制截面图
    plot_gain_cross_sections(df, beam_id)
    
    # 分析覆盖特性
    analyze_beam_coverage(df, gain_threshold_dB=-3)
    
    print(f"\n分析完成！图表已保存为:")
    print(f"  - antenna_gain_beam_{beam_id}.png")
    print(f"  - antenna_gain_cross_sections_beam_{beam_id}.png")

if __name__ == "__main__":
    main()
